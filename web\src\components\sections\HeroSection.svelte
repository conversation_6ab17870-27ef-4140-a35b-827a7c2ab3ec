<script>
  import { fly } from 'svelte/transition';
  import { Button } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import { Clock, Target, Zap, X, Search, FileText, Send, ArrowLeftRight } from 'lucide-svelte';

  let visible = $state(false);
  let automationStep = $state(0);
  let sliderPosition = $state(0); // 0 = show chaos, 100 = show automation
  let isDragging = $state(false);
  let stickyNoteAnimations = $state([]);

  $effect(() => {
    visible = true;

    // Automation step animations
    const automationTimer = setInterval(() => {
      automationStep = (automationStep + 1) % 4;
    }, 2500);

    // Auto-slide demo every 6 seconds
    const slideTimer = setInterval(() => {
      if (!isDragging) {
        sliderPosition = sliderPosition === 0 ? 100 : 0;
      }
    }, 6000);

    // Random sticky note animations
    const stickyTimer = setInterval(() => {
      const randomIndex = Math.floor(Math.random() * 12);
      stickyNoteAnimations = [...stickyNoteAnimations, randomIndex].slice(-2);
    }, 2000);

    return () => {
      clearInterval(automationTimer);
      clearInterval(slideTimer);
      clearInterval(stickyTimer);
    };
  });

  // Handle slider drag
  function handleSliderDrag(event) {
    if (!isDragging) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    sliderPosition = percentage;
  }

  function startDrag() {
    isDragging = true;
  }

  function stopDrag() {
    isDragging = false;
  }
</script>

<section
  class="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
  <div class="relative z-10 flex min-h-screen flex-col">
    <!-- Main heading -->
    {#if visible}
      <div class="pb-12 pt-20 text-center">
        <div
          in:fly={{ y: 30, duration: 1000 }}
          class="mb-8 text-5xl font-bold tracking-tight md:text-6xl lg:text-7xl">
          Stop <span class="bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent"
            >struggling</span>
          with job hunting.
          <br />
          Start
          <span
            class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent"
            >automating</span> your success.
        </div>
        <p
          in:fly={{ y: 30, duration: 1000, delay: 200 }}
          class="mx-auto max-w-4xl text-xl font-light leading-relaxed text-slate-600 md:text-2xl dark:text-slate-300">
          Drag the slider to see the transformation from chaos to automation
        </p>
      </div>
    {/if}

    <!-- Desktop Monitor with Slider -->
    <div class="flex-1 px-4 lg:px-8">
      {#if visible}
        <div in:fly={{ y: 50, duration: 1000, delay: 400 }} class="relative mx-auto max-w-6xl">
          <!-- Ultra-Modern Monitor Stand (Apple Studio Display inspired) -->
          <div class="mb-8 flex flex-col items-center">
            <!-- Monitor Base - Sleek and minimal -->
            <div
              class="h-2 w-40 rounded-full bg-gradient-to-r from-slate-200 via-slate-300 to-slate-200 shadow-lg">
            </div>
            <!-- Monitor Arm - Clean geometric design -->
            <div
              class="-mt-1 h-20 w-4 bg-gradient-to-b from-slate-300 to-slate-400 shadow-sm"
              style="clip-path: polygon(25% 0%, 75% 0%, 100% 100%, 0% 100%)">
            </div>
          </div>

          <!-- Ultra-Modern Monitor Frame (Inspired by Apple Studio Display / Dell UltraSharp) -->
          <div
            class="relative rounded-2xl border border-slate-300 bg-gradient-to-b from-slate-100 to-slate-200 shadow-2xl"
            style="aspect-ratio: 16/10; padding: 4px;">
            <!-- Ultra-thin bezel -->
            <div
              class="absolute inset-1 rounded-2xl bg-gradient-to-b from-slate-50 to-slate-100 shadow-inner">
            </div>

            <!-- Screen with realistic proportions -->
            <div
              class="relative h-full w-full cursor-grab overflow-hidden rounded-2xl bg-white shadow-inner active:cursor-grabbing"
              on:mousemove={handleSliderDrag}
              on:mouseup={stopDrag}
              on:mouseleave={stopDrag}
              role="slider"
              tabindex="0"
              aria-valuemin="0"
              aria-valuemax="100"
              aria-valuenow={sliderPosition}
              aria-label="Drag to compare manual vs automated job hunting">
              <!-- Subtle screen reflection -->
              <div
                class="from-white/3 pointer-events-none absolute inset-0 z-50 rounded bg-gradient-to-br via-transparent to-transparent">
              </div>
              <!-- Modern Browser Window -->
              <div class="absolute inset-0 bg-white">
                <!-- Modern Browser Header (Chrome/Safari inspired) -->
                <div
                  class="flex items-center justify-between border-b border-slate-200 bg-white px-4 py-3">
                  <div class="flex items-center space-x-3">
                    <!-- macOS-style window controls -->
                    <div class="h-3 w-3 rounded-full bg-red-500"></div>
                    <div class="h-3 w-3 rounded-full bg-yellow-500"></div>
                    <div class="h-3 w-3 rounded-full bg-green-500"></div>
                  </div>

                  <!-- Address bar area -->
                  <div class="mx-6 flex-1">
                    <div
                      class="mx-auto max-w-md rounded-lg bg-slate-100 px-4 py-2 text-sm text-slate-600">
                      indeed.com/jobs?q=software+engineer&l=San+Francisco...
                    </div>
                  </div>

                  <!-- Browser controls -->
                  <div class="flex items-center space-x-2 text-slate-400">
                    <div class="flex h-6 w-6 items-center justify-center rounded bg-slate-100">
                      <div class="h-3 w-3 rounded-sm border border-slate-400"></div>
                    </div>
                    <div class="text-xs font-medium text-red-600">47 tabs</div>
                  </div>
                </div>

                <!-- Modern Browser Tabs (Chrome-style) -->
                <div class="flex overflow-hidden border-b border-slate-200 bg-slate-50">
                  {#each Array(8) as _, i}
                    <div
                      class="flex min-w-0 max-w-48 items-center border-r border-slate-200 bg-white px-3 py-2
                      {i === 0 ? 'bg-white' : 'bg-slate-50 hover:bg-slate-100'}
                      {i < 2 ? 'border-b-2 border-red-400' : ''}">
                      <!-- Favicon -->
                      <div
                        class="mr-2 h-4 w-4 flex-shrink-0 rounded-sm bg-gradient-to-br from-blue-500 to-blue-600">
                      </div>
                      <!-- Tab title -->
                      <span class="truncate text-xs text-slate-700">
                        {[
                          'Indeed - Software Engineer Jobs',
                          'LinkedIn - Job Search',
                          'Glassdoor - Company Reviews',
                          'Monster - Find Jobs',
                          'ZipRecruiter - Apply Now',
                          'AngelList - Startup Jobs',
                          'Remote.co - Remote Work',
                          'FlexJobs - Flexible Work',
                        ][i]}
                      </span>
                      <!-- Close button -->
                      <div
                        class="ml-2 flex h-4 w-4 flex-shrink-0 items-center justify-center rounded hover:bg-slate-200">
                        <div class="h-2 w-2 text-slate-400">×</div>
                      </div>
                    </div>
                  {/each}

                  <!-- Overflow indicator -->
                  <div class="flex items-center border-l border-red-200 bg-red-50 px-3 py-2">
                    <span class="text-xs font-medium text-red-600">+39 more tabs</span>
                  </div>
                </div>

                <!-- Browser Content with Sticky Notes -->
                <div
                  class="relative h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 p-4">
                  <!-- Error Messages and Chaos -->
                  <div class="mb-4 space-y-3">
                    <div
                      class="rounded-lg border border-red-300 bg-gradient-to-r from-red-50 to-red-100 p-3 shadow-md">
                      <div class="flex items-center text-red-700">
                        <X class="mr-2 h-4 w-4" />
                        <div>
                          <div class="font-semibold">
                            Application Error - Session timeout after 2 hours
                          </div>
                          <div class="mt-1 text-xs text-red-600">
                            Lost all form data. Need to start over...
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="rounded-lg border border-yellow-300 bg-gradient-to-r from-yellow-50 to-yellow-100 p-3 shadow-md">
                      <div class="text-yellow-800">
                        <div class="font-semibold">
                          📄 Cover_Letter_Template_v47_FINAL_FINAL_USE_THIS.docx
                        </div>
                        <div class="mt-1 text-xs text-yellow-700">Last modified: 3 hours ago</div>
                      </div>
                    </div>
                    <div
                      class="rounded-lg border border-orange-300 bg-gradient-to-r from-orange-50 to-orange-100 p-3 shadow-md">
                      <div class="text-orange-800">
                        <div class="font-semibold">
                          📊 Job_Applications_Tracker_2024_UPDATED_v3_REAL.xlsx
                        </div>
                        <div class="mt-1 text-xs text-orange-700">
                          Status: Completely out of sync
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Lots of Sticky Notes scattered everywhere -->
                  <div
                    class="absolute left-6 top-16 z-10 -rotate-3 transform rounded bg-yellow-300 p-2 text-xs shadow-md">
                    🔔 Follow up Google!
                  </div>
                  <div
                    class="absolute right-8 top-20 z-10 rotate-2 transform rounded bg-pink-300 p-2 text-xs shadow-md">
                    ✏️ Update resume
                  </div>
                  <div
                    class="absolute left-12 top-32 z-10 -rotate-1 transform rounded bg-blue-300 p-2 text-xs shadow-md">
                    📝 Applied to 5 jobs
                  </div>
                  <div
                    class="absolute right-16 top-40 z-10 rotate-3 transform rounded bg-green-300 p-2 text-xs shadow-md">
                    📚 Interview prep
                  </div>
                  <div
                    class="absolute left-1/3 top-24 z-10 rotate-1 transform rounded bg-purple-300 p-2 text-xs shadow-md">
                    � Salary research
                  </div>
                  <div
                    class="absolute bottom-20 left-8 z-10 -rotate-2 transform rounded bg-orange-300 p-2 text-xs shadow-md">
                    📞 Call recruiter
                  </div>
                  <div
                    class="absolute bottom-16 right-12 z-10 rotate-1 transform rounded bg-red-300 p-2 text-xs shadow-md">
                    ⏰ Deadline today!
                  </div>
                  <div
                    class="absolute left-1/4 top-1/2 z-10 -rotate-1 transform rounded bg-cyan-300 p-2 text-xs shadow-md">
                    🎯 Target companies
                  </div>
                  <div
                    class="absolute right-1/4 top-1/2 z-10 rotate-2 transform rounded bg-lime-300 p-2 text-xs shadow-md">
                    📊 Track applications
                  </div>
                  <div
                    class="absolute bottom-1/3 left-1/2 z-10 -rotate-3 transform rounded bg-indigo-300 p-2 text-xs shadow-md">
                    🔍 Job search tips
                  </div>

                  <!-- Stress Indicator -->
                  <div
                    class="absolute bottom-4 left-4 right-4 rounded border border-red-300 bg-red-100 p-2">
                    <div class="flex items-center text-xs text-red-700">
                      <Clock class="mr-1 h-3 w-3" />
                      <span class="font-semibold">6 hours • 3 applications • 0 responses</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Modern Automation Dashboard Overlay -->
              <div
                class="absolute inset-0 bg-white transition-transform duration-700 ease-in-out"
                style="transform: translateX({sliderPosition}%)">
                <!-- Modern Dashboard Header -->
                <div
                  class="flex items-center justify-between border-b border-slate-200 bg-white px-4 py-3">
                  <div class="flex items-center space-x-3">
                    <!-- macOS-style window controls -->
                    <div class="h-3 w-3 rounded-full bg-red-500"></div>
                    <div class="h-3 w-3 rounded-full bg-yellow-500"></div>
                    <div class="h-3 w-3 rounded-full bg-green-500"></div>
                  </div>

                  <!-- Dashboard title -->
                  <div class="mx-6 flex-1">
                    <div
                      class="mx-auto max-w-md rounded-lg bg-slate-100 px-4 py-2 text-sm text-slate-600">
                      hirli.ai/dashboard - AI Job Automation
                    </div>
                  </div>

                  <!-- Status indicator -->
                  <div class="flex items-center space-x-2">
                    <div class="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
                    <span class="text-xs text-slate-600">Active</span>
                  </div>
                </div>

                <!-- Modern Dashboard Content -->
                <div class="h-full bg-slate-50 p-6">
                  <!-- Clean Stats Cards -->
                  <div class="mb-6 grid grid-cols-3 gap-4">
                    <div class="rounded-xl border border-slate-200 bg-white p-4 shadow-sm">
                      <div class="text-2xl font-bold text-slate-900">247</div>
                      <div class="text-sm text-slate-600">Applications Sent</div>
                      <div class="mt-1 text-xs text-green-600">↗ +23 this week</div>
                    </div>
                    <div class="rounded-xl border border-slate-200 bg-white p-4 shadow-sm">
                      <div class="text-2xl font-bold text-slate-900">38</div>
                      <div class="text-sm text-slate-600">Interview Requests</div>
                      <div class="mt-1 text-xs text-blue-600">↗ +8 this week</div>
                    </div>
                    <div class="rounded-xl border border-slate-200 bg-white p-4 shadow-sm">
                      <div class="text-2xl font-bold text-slate-900">15.4%</div>
                      <div class="text-sm text-slate-600">Response Rate</div>
                      <div class="mt-1 text-xs text-purple-600">↗ +2.1% this week</div>
                    </div>
                  </div>

                  <!-- Automation Steps -->
                  <div class="space-y-2">
                    <div
                      class="rounded-lg border border-slate-200/50 bg-white/95 p-3 backdrop-blur-sm
                      {automationStep >= 0
                        ? 'bg-gradient-to-r from-green-50 to-green-100 ring-2 ring-green-300'
                        : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <Search class="mr-2 h-4 w-4 text-green-600" />
                          <span>AI Job Scanning</span>
                        </div>
                        <Badge class="bg-green-100 text-xs text-green-700">Active</Badge>
                      </div>
                    </div>

                    <div
                      class="rounded-lg border border-slate-200/50 bg-white/95 p-3 backdrop-blur-sm
                      {automationStep >= 1
                        ? 'bg-gradient-to-r from-blue-50 to-blue-100 ring-2 ring-blue-300'
                        : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <Target class="mr-2 h-4 w-4 text-blue-600" />
                          <span>Smart Matching</span>
                        </div>
                        <Badge class="bg-blue-100 text-xs text-blue-700">Processing</Badge>
                      </div>
                    </div>

                    <div
                      class="rounded-lg border border-slate-200/50 bg-white/95 p-3 backdrop-blur-sm
                      {automationStep >= 2
                        ? 'bg-gradient-to-r from-purple-50 to-purple-100 ring-2 ring-purple-300'
                        : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <FileText class="mr-2 h-4 w-4 text-purple-600" />
                          <span>Application Generation</span>
                        </div>
                        <Badge class="bg-purple-100 text-xs text-purple-700">AI-Powered</Badge>
                      </div>
                    </div>

                    <div
                      class="rounded-lg border border-slate-200/50 bg-white/95 p-3 backdrop-blur-sm
                      {automationStep >= 3
                        ? 'bg-gradient-to-r from-green-50 to-green-100 ring-2 ring-green-300'
                        : ''}">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm font-medium">
                          <Send class="mr-2 h-4 w-4 text-green-600" />
                          <span>Bulk Submission</span>
                        </div>
                        <Badge class="bg-green-100 text-xs text-green-700">Complete</Badge>
                      </div>
                    </div>
                  </div>

                  <!-- Success Indicator -->
                  <div
                    class="absolute bottom-4 left-4 right-4 rounded-lg border border-green-300 bg-gradient-to-r from-green-100 to-green-200 p-2">
                    <div class="flex items-center text-green-800">
                      <Zap class="mr-2 h-4 w-4" />
                      <span class="text-xs font-bold"
                        >15 minutes • 50+ applications • 15% response rate</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Interactive Slider Handle -->
              <div
                class="group absolute bottom-0 top-0 z-20 w-1 cursor-col-resize bg-white/80 shadow-lg backdrop-blur-sm"
                style="left: {sliderPosition}%"
                on:mousedown={startDrag}
                on:mousemove={handleSliderDrag}
                on:mouseup={stopDrag}
                on:mouseleave={stopDrag}
                role="slider"
                tabindex="0"
                aria-label="Drag to compare manual vs automated job hunting">
                <!-- Slider Handle -->
                <div
                  class="absolute left-1/2 top-1/2 flex h-8 w-8 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full border-2 border-slate-300 bg-white shadow-xl transition-transform group-hover:scale-110">
                  <ArrowLeftRight class="h-4 w-4 text-slate-600" />
                </div>

                <!-- Labels -->
                <div
                  class="absolute -top-8 left-1/2 -translate-x-1/2 whitespace-nowrap rounded bg-black/70 px-2 py-1 text-xs font-medium text-white">
                  Drag to compare
                </div>
              </div>
            </div>
          </div>

          <!-- Comparison Labels -->
          <div class="mt-6 flex justify-between text-center">
            <div class="flex-1">
              <div class="mb-2 text-lg font-bold text-red-600">Manual Job Hunting</div>
              <div class="text-sm text-slate-600 dark:text-slate-400">
                Chaotic • Time-consuming • Stressful
              </div>
            </div>
            <div class="flex-1">
              <div class="mb-2 text-lg font-bold text-green-600">AI Automation</div>
              <div class="text-sm text-slate-600 dark:text-slate-400">
                Organized • Efficient • Results-driven
              </div>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Modern CTA Section -->
  {#if visible}
    <div class="px-4 pb-20 text-center">
      <div in:fly={{ y: 30, duration: 1000, delay: 800 }} class="mb-12">
        <h3
          class="mb-4 bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-3xl font-bold text-transparent md:text-4xl dark:from-white dark:to-slate-300">
          Ready to transform your job search?
        </h3>
        <p class="mx-auto max-w-2xl text-lg text-slate-600 dark:text-slate-400">
          Join thousands of job seekers who've made the switch from chaos to automation
        </p>
      </div>

      <div
        in:fly={{ y: 30, duration: 1000, delay: 1000 }}
        class="mb-12 flex flex-col justify-center space-y-4 sm:flex-row sm:space-x-6 sm:space-y-0">
        <Button
          class="rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-4 text-lg font-semibold text-white shadow-lg shadow-blue-500/25 transition-all duration-300 hover:scale-105 hover:from-blue-700 hover:to-purple-700 hover:shadow-xl hover:shadow-blue-500/40">
          Start Automating Now
        </Button>
        <Button
          variant="outline"
          class="rounded-xl border-2 border-slate-300 px-8 py-4 text-lg font-semibold transition-all duration-300 hover:border-slate-400 hover:bg-slate-50 dark:hover:bg-slate-800">
          Watch Demo
        </Button>
      </div>

      <div
        in:fly={{ y: 30, duration: 1000, delay: 1200 }}
        class="flex items-center justify-center text-sm text-slate-500 dark:text-slate-400">
        <div class="mr-4 flex -space-x-2">
          <img
            src="https://randomuser.me/api/portraits/women/79.jpg"
            alt="User"
            class="h-10 w-10 rounded-full border-2 border-white shadow-lg" />
          <img
            src="https://randomuser.me/api/portraits/men/32.jpg"
            alt="User"
            class="h-10 w-10 rounded-full border-2 border-white shadow-lg" />
          <img
            src="https://randomuser.me/api/portraits/women/44.jpg"
            alt="User"
            class="h-10 w-10 rounded-full border-2 border-white shadow-lg" />
        </div>
        <span class="font-medium"
          >Join <span class="font-bold text-slate-700 dark:text-slate-300">10,000+</span> successful
          job seekers</span>
      </div>
    </div>
  {/if}
</section>
